<!--@subject {{trans "Invoice for your %store_name order" store_name=$store.frontend_name}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var frontend_base_url":"Frontend Base URL",
"var order_data.customer_name":"Customer Name",
"var comment":"Invoice Comment",
"var invoice.increment_id":"Invoice Id",
"layout area=\"frontend\" handle=\"sales_email_order_invoice_items\" invoice=$invoice order=$order":"Invoice Items Grid",
"var order.increment_id":"Order Id",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.shipping_description":"Shipping Description"
} @-->
{{template config_path="design/email/header_template"}}
{{layout handle="invoice_markup" invoice=$invoice invoice_id=$invoice_id area="frontend"}}
<table align="center" style="display: block; background-color:#000; text-align:center; width: 660px">
    <tbody style="display:block;">
    <tr style="display:block;">
        <td class="dark"  style="display:block; padding-bottom:8px; padding-top:5px; background-color:#000">
            <h3 style="text-align: center;">
                {{trans "Hi %name," name=$order_data.customer_name}}
            </h3>
        </td>
    </tr>
    <tr style="display:block;">
        <td class="dark" align="center" style="display:block; padding-bottom:0px; background-color:#000">
            <h1 style="text-align: center; margin: 0 !important">
                {{trans "Here\'s your Invoice :)" }}
            </h1>
        </td>
    </tr>
    <tr style="display:block;">
        <td class="dark" align="center" style="display:block; padding-bottom:8px; background-color:#000">
            <h3 style="text-align: center; letter-spacing: 0.025em;">
                {{trans "ORDER NUMBER: %increment_id." increment_id=$order.increment_id |raw}}
            </h3>
        </td>
    </tr>
    </tbody>
</table>
<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
    <tbody>
    <tr>
        <td align="center" style="padding-top: 10px;padding-bottom:10px;">
            <h2 style="text-align: center; margin: 0 0 20px 0 !important">
                {{trans 'Your Invoice #%invoice_id' invoice_id=$invoice.increment_id |raw}}
            </h2>
        </td>
    </tr>
    <tr>
        <td style="margin-left: 0px">
            <p style="margin: 0 !important;">
                {{trans 'You can check the status of your order by clicking the button below.'}}
            </p>
        </td>
    </tr>
    <tr>
        <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody style="display: block">
                <tr style="display: block">
                    <td style="display: block">
                        <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
                            <tr>
                                <td align="center" style="padding: 8px 0 !important">
                                    <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}} (V7)</a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </tr>
    <tr>
        <td style="margin-left: 0px">
            <p style="margin-top: 35px !important;">
                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
            </p>
        </td>
    </tr>
    </tbody>
</table>

<table style="width: 660px">
    <tr class="email-information">
        <td>
            {{depend order_data.email_customer_note}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order_data.email_customer_note|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}

            {{layout handle="weltpixel_sales_email_order_invoice_items" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area="frontend"}}

            <table class="order-details" style="border-top: 5px solid #000000">
                <tr>
                    <td class="address-details" style="padding-top: 60px !important">
                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="address-details" style="padding-top: 60px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    <td class="method-info wp-method-info" style="padding-bottom: 60px !important">
                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="method-info" style="padding-bottom: 60px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
                        <p style="color: #555656;">{{var order.shipping_description}}</p>
                    </td>
                    {{/depend}}
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
