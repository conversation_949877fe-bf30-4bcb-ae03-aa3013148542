<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Email:etc/email_templates.xsd">
    <!-- Order Confirmation Templates -->
    <template id="comave_order_new_template" label="New Order - Comave Custom" file="order_new_comave.html" type="html" module="Comave_Sales" area="frontend"/>
    <template id="comave_order_new_guest_template" label="New Order Guest - Comave Custom" file="order_new_guest_comave.html" type="html" module="Comave_Sales" area="frontend"/>

    <!-- Order Update Templates -->
    <template id="comave_order_update_template" label="Order Update - Comave Custom" file="order_update_comave.html" type="html" module="Comave_Sales" area="frontend"/>
    <template id="comave_order_update_guest_template" label="Order Update Guest - Comave Custom" file="order_update_guest_comave.html" type="html" module="Comave_Sales" area="frontend"/>

    <!-- Invoice Templates -->
    <template id="comave_new_invoice_template" label="New Invoice - Comave Custom" file="new_invoice_comave.html" type="html" module="Comave_Sales" area="frontend"/>
    <template id="comave_new_invoice_guest_template" label="New Invoice Guest - Comave Custom" file="new_invoice_guest_comave.html" type="html" module="Comave_Sales" area="frontend"/>

    <!-- Shipment Templates -->
    <template id="comave_new_shipment_template" label="New Shipment - Comave Custom" file="new_shipment_comave.html" type="html" module="Comave_Sales" area="frontend"/>
    <template id="comave_new_shipment_guest_template" label="New Shipment Guest - Comave Custom" file="new_shipment_guest_comave.html" type="html" module="Comave_Sales" area="frontend"/>

    <!-- Shipment Update Templates -->
    <template id="comave_shipment_update_template" label="Shipment Update - Comave Custom" file="shipment_update_comave.html" type="html" module="Comave_Sales" area="frontend"/>
    <template id="comave_shipment_update_guest_template" label="Shipment Update Guest - Comave Custom" file="shipment_update_guest_comave.html" type="html" module="Comave_Sales" area="frontend"/>
</config>
