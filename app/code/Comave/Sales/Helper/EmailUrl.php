<?php

declare(strict_types=1);

namespace Comave\Sales\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\DeploymentConfig;

class EmailUrl extends AbstractHelper
{
    public function __construct(
        Context $context,
        private readonly DeploymentConfig $deploymentConfig
    ) {
        parent::__construct($context);
    }

    /**
     * Get frontend base URL from env.php
     *
     * @return string
     */
    public function getFrontendBaseUrl(): string
    {
        $frontendBaseUrl = $this->deploymentConfig->get('frontend_base_url');
        
        if ($frontendBaseUrl) {
            return rtrim($frontendBaseUrl, '/');
        }
        
        // Fallback to store base URL if frontend_base_url is not set
        return rtrim($this->_getRequest()->getScheme() . '://' . $this->_getRequest()->getHttpHost(), '/');
    }

    /**
     * Generate frontend order URL
     *
     * @param int $orderId
     * @return string
     */
    public function getFrontendOrderUrl(int $orderId): string
    {
        return $this->getFrontendBaseUrl() . '/en/profile/orders/' . $orderId;
    }
}
