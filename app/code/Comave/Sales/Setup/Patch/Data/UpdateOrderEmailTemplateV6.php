<?php

declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\CouldNotSaveException;

class UpdateOrderEmailTemplateV6 implements DataPatchInterface
{
    private const ORDER_EMAIL_TEMPLATE = 'order_new_comave.html';
    private const TEMPLATE_CODE = 'New Order - comave';
    private const DEFAULT_SOURCE_DIR = 'install-data';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $htmlContent = $this->getFileContent(self::ORDER_EMAIL_TEMPLATE);
        if (!$htmlContent) {
            return;
        }

        try {
            $this->moduleDataSetup->getConnection()->update(
                $this->moduleDataSetup->getTable('email_template'),
                ['template_text' => $htmlContent],
                ['template_code = ?' => self::TEMPLATE_CODE]
            );
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to update the order email template ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Get dependencies
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * Get aliases
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * Get file content from install-data directory
     */
    private function getFileContent(string $fileName): string
    {
        $moduleDir = $this->dirReader->getModuleDir('', 'Comave_Sales');
        $filePath = $moduleDir . DIRECTORY_SEPARATOR . 'etc' . DIRECTORY_SEPARATOR . self::DEFAULT_SOURCE_DIR . DIRECTORY_SEPARATOR . $fileName;

        if (!$this->ioFile->fileExists($filePath)) {
            $this->logger->error('Email template file not found: ' . $filePath);
            return '';
        }

        return $this->ioFile->read($filePath);
    }
}
