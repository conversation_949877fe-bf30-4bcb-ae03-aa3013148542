<?php

declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\CouldNotSaveException;

class FixAllEmailTemplateUrlsV7 implements DataPatchInterface
{
    /**
     * Template mappings: template_code => template_file
     */
    private const TEMPLATE_MAPPINGS = [
        'order update - comave' => 'order_update_comave.html',
        'Order Update for Guest - comave' => 'order_update_guest_comave.html',
        'New Invoice- comave' => 'new_invoice_comave.html',
        'New Invoice for Guest - comave' => 'new_invoice_guest_comave.html',
        'New Shipment - comave' => 'new_shipment_comave.html',
        'New Shipment Guest- comave' => 'new_shipment_guest_comave.html',
        'Shipment Update - comave' => 'shipment_update_comave.html',
        'Shipment Update Guest - comave' => 'shipment_update_guest_comave.html'
    ];

    private const DEFAULT_SOURCE_DIR = 'install-data';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $this->updateEmailTemplatesFromFiles();
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to update email templates: ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Update email templates from template files
     */
    private function updateEmailTemplatesFromFiles(): void
    {
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');

        foreach (self::TEMPLATE_MAPPINGS as $templateCode => $templateFile) {
            $this->logger->info("Updating template: {$templateCode} from file: {$templateFile}");

            $htmlContent = $this->getFileContent($templateFile);
            if (!$htmlContent) {
                $this->logger->warning("Template file not found: {$templateFile}");
                continue;
            }

            // Check if template exists
            $select = $connection->select()
                ->from($tableName, ['template_id'])
                ->where('template_code = ?', $templateCode);

            $templateId = $connection->fetchOne($select);

            if ($templateId) {
                // Update existing template
                $connection->update(
                    $tableName,
                    ['template_text' => $htmlContent],
                    ['template_code = ?' => $templateCode]
                );

                $this->logger->info("Updated template: {$templateCode}");
            } else {
                $this->logger->warning("Template not found in database: {$templateCode}");
            }
        }
    }

    /**
     * Get file content from install-data directory
     */
    private function getFileContent(string $fileName): string
    {
        $moduleDir = $this->dirReader->getModuleDir('', 'Comave_Sales');
        $filePath = $moduleDir . DIRECTORY_SEPARATOR . 'etc' . DIRECTORY_SEPARATOR . self::DEFAULT_SOURCE_DIR . DIRECTORY_SEPARATOR . $fileName;

        if (!$this->ioFile->fileExists($filePath)) {
            $this->logger->error('Email template file not found: ' . $filePath);
            return '';
        }

        return $this->ioFile->read($filePath);
    }

    /**
     * Get dependencies
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * Get aliases
     */
    public function getAliases(): array
    {
        return [];
    }
}
