<?php

declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\CouldNotSaveException;

class FixAllEmailTemplateUrlsV7 implements DataPatchInterface
{
    /**
     * Template codes that need URL fixing
     */
    private const TEMPLATE_CODES_TO_FIX = [
        'order update - comave',
        'Order Update for Guest - comave', 
        'New Invoice- comave',
        'New Invoice for Guest - comave',
        'New Shipment - comave',
        'New Shipment Guest- comave',
        'Shipment Update - comave',
        'Shipment Update Guest - comave'
    ];

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $this->fixEmailTemplateUrls();
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to fix email template URLs: ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Fix URLs in all problematic email templates
     */
    private function fixEmailTemplateUrls(): void
    {
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');

        foreach (self::TEMPLATE_CODES_TO_FIX as $templateCode) {
            $this->logger->info("Fixing URLs for template: {$templateCode}");
            
            // Get current template content
            $select = $connection->select()
                ->from($tableName, ['template_text'])
                ->where('template_code = ?', $templateCode);
            
            $templateText = $connection->fetchOne($select);
            
            if ($templateText) {
                // Fix various problematic URL patterns
                $updatedText = $this->fixUrlsInTemplate($templateText);
                
                if ($updatedText !== $templateText) {
                    // Update the template
                    $connection->update(
                        $tableName,
                        ['template_text' => $updatedText],
                        ['template_code = ?' => $templateCode]
                    );
                    
                    $this->logger->info("Updated URLs for template: {$templateCode}");
                } else {
                    $this->logger->info("No URL changes needed for template: {$templateCode}");
                }
            } else {
                $this->logger->warning("Template not found: {$templateCode}");
            }
        }
    }

    /**
     * Fix URLs in template content
     */
    private function fixUrlsInTemplate(string $templateText): string
    {
        // Replace problematic URL patterns with frontend_base_url
        $patterns = [
            // Fix customer account login URLs
            '/\{\{var this\.getUrl\(\$store,\s*[\'"]customer\/account\/[\'"][^}]*\}\}/' => '{{var frontend_base_url}}/en/profile/orders',
            '/\{\{var this\.getUrl\([\'"]customer\/account\/[\'"][^}]*\}\}/' => '{{var frontend_base_url}}/en/profile/orders',
            
            // Fix sales order history URLs  
            '/\{\{var this\.getUrl\(\$store,\s*[\'"]sales\/order\/history\/[\'"][^}]*\}\}/' => '{{var frontend_base_url}}/en/profile/orders',
            '/\{\{var this\.getUrl\([\'"]sales\/order\/history\/[\'"][^}]*\}\}/' => '{{var frontend_base_url}}/en/profile/orders',
            
            // Fix getFrontendUrl calls
            '/\{\{var this\.getFrontendUrl\([^}]*\}\}/' => '{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}',
            
            // Fix hardcoded backend URLs
            '/https?:\/\/[a-zA-Z0-9.-]*\.ddev\.site\/[^"\']*/' => '{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}',
            '/https?:\/\/mc[a-zA-Z0-9.-]*\.comave\.com\/[^"\']*/' => '{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}',
        ];

        $updatedText = $templateText;
        
        foreach ($patterns as $pattern => $replacement) {
            $updatedText = preg_replace($pattern, $replacement, $updatedText);
        }

        return $updatedText;
    }

    /**
     * Get dependencies
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * Get aliases
     */
    public function getAliases(): array
    {
        return [];
    }
}
