<?php
/**
 * Copyright © ComAve, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Email\Model\ResourceModel\Template as TemplateResource;
use Magento\Email\Model\TemplateFactory;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Psr\Log\LoggerInterface;

/**
 * Update all ComAve email templates to fix frontend URLs (V7)
 */
class UpdateAllEmailTemplatesV7 implements DataPatchInterface, PatchRevertableInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @var TemplateFactory
     */
    private $templateFactory;

    /**
     * @var TemplateResource
     */
    private $templateResource;

    /**
     * @var State
     */
    private $appState;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var array
     */
    private $templateMappings = [
        'order update - comave' => 'order_update_comave.html',
        'Order Update for Guest - comave' => 'order_update_guest_comave.html',
        'New Invoice- comave' => 'new_invoice_comave.html',
        'New Invoice for Guest - comave' => 'new_invoice_guest_comave.html',
        'New Shipment - comave' => 'new_shipment_comave.html',
        'New Shipment Guest- comave' => 'new_shipment_guest_comave.html',
        'Shipment Update - comave' => 'shipment_update_comave.html',
        'Shipment Update Guest - comave' => 'shipment_update_guest_comave.html'
    ];

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param TemplateFactory $templateFactory
     * @param TemplateResource $templateResource
     * @param State $appState
     * @param LoggerInterface $logger
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        TemplateFactory $templateFactory,
        TemplateResource $templateResource,
        State $appState,
        LoggerInterface $logger
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->templateFactory = $templateFactory;
        $this->templateResource = $templateResource;
        $this->appState = $appState;
        $this->logger = $logger;
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area code already set
        }

        foreach ($this->templateMappings as $templateCode => $templateFile) {
            try {
                $this->updateEmailTemplate($templateCode, $templateFile);
                $this->logger->info("Successfully updated email template: {$templateCode}");
            } catch (\Exception $e) {
                $this->logger->error("Failed to update email template {$templateCode}: " . $e->getMessage());
            }
        }

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    /**
     * Update individual email template
     *
     * @param string $templateCode
     * @param string $templateFile
     * @throws \Exception
     */
    private function updateEmailTemplate(string $templateCode, string $templateFile): void
    {
        $templatePath = __DIR__ . '/../../etc/install-data/' . $templateFile;
        
        if (!file_exists($templatePath)) {
            throw new \Exception("Template file not found: {$templatePath}");
        }

        $templateContent = file_get_contents($templatePath);
        if ($templateContent === false) {
            throw new \Exception("Could not read template file: {$templatePath}");
        }

        // Update template in database
        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');

        $connection->update(
            $tableName,
            ['template_text' => $templateContent],
            ['template_code = ?' => $templateCode]
        );

        $affectedRows = $connection->rowCount();
        if ($affectedRows === 0) {
            $this->logger->warning("No rows updated for template: {$templateCode}");
        }
    }

    /**
     * @inheritdoc
     */
    public function revert()
    {
        // Revert is not implemented as it would require storing original templates
        // Manual database restore would be needed if revert is required
    }

    /**
     * @inheritdoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies()
    {
        return [];
    }
}
