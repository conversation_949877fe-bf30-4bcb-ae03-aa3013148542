<?php

declare(strict_types=1);

namespace Comave\Sales\Block\Email;

use Magento\Framework\View\Element\Template;
use Comave\Sales\Helper\EmailUrl;

class OrderUrl extends Template
{
    public function __construct(
        Template\Context $context,
        private readonly EmailUrl $emailUrlHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Get frontend base URL from env.php
     *
     * @return string
     */
    public function getFrontendBaseUrl(): string
    {
        return $this->emailUrlHelper->getFrontendBaseUrl();
    }

    /**
     * Generate frontend order URL
     *
     * @param int $orderId
     * @return string
     */
    public function getFrontendOrderUrl(int $orderId): string
    {
        return $this->emailUrlHelper->getFrontendOrderUrl($orderId);
    }
}
